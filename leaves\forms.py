from django import forms
from .models import LeaveType, LeaveBalance, Leave, Departure
from employees.models import Employee

class LeaveTypeForm(forms.ModelForm):
    class Meta:
        model = LeaveType
        fields = ['name', 'description', 'max_days_per_year']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

class LeaveBalanceForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    # Read-only field for displaying remaining balance
    remaining_balance = forms.IntegerField(
        label='الرصيد المتبقي',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'remaining_balance_display'})
    )

    # Hidden field for used balance (will be calculated automatically)
    used_balance = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = LeaveBalance
        fields = ['employee', 'leave_type', 'year', 'initial_balance', 'used_balance']
        widgets = {
            'employee': forms.HiddenInput(),
        }

class LeaveForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'id': 'ministry_number_input'})
    )

    # Display field for employee name (not saved to model, just for display)
    employee_name = forms.CharField(
        label='اسم الموظف',
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'readonly': 'readonly', 'id': 'employee_name_display'})
    )

    class Meta:
        model = Leave
        fields = ['employee', 'leave_type', 'start_date', 'end_date', 'days_count', 'reason']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control', 'id': 'id_employee'}),
            'leave_type': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'days_count': forms.NumberInput(attrs={'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        employee = cleaned_data.get('employee')
        leave_type = cleaned_data.get('leave_type')

        # Check if start_date is before end_date
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(
                'تاريخ بداية الإجازة يجب أن يكون قبل تاريخ نهاية الإجازة'
            )

        # Check for overlapping leaves if employee and dates are provided
        if employee and start_date and end_date and leave_type:
            # Get the ID of the current leave being edited (if any)
            current_leave_id = self.instance.id if self.instance and self.instance.pk else None

            # Check for overlapping leaves, excluding the current leave being edited
            overlapping_leaves = Leave.objects.filter(
                employee=employee,
                status='approved',
                start_date__lte=end_date,
                end_date__gte=start_date
            )

            # Exclude current leave if editing
            if current_leave_id:
                overlapping_leaves = overlapping_leaves.exclude(id=current_leave_id)

            if overlapping_leaves.exists():
                # Get details of the first overlapping leave for the error message
                overlap = overlapping_leaves.first()
                raise forms.ValidationError(
                    f'الموظف لديه إجازة مسجلة بالفعل خلال هذه الفترة (من {overlap.start_date} إلى {overlap.end_date})'
                )

        return cleaned_data

class DepartureForm(forms.ModelForm):
    # Hidden field for employee ID
    employee_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Display field for ministry number (not saved to model)
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control employee-search-input',
                                     'data-employee-id-target': 'id_employee_id',
                                     'data-employee-name-target': 'employee_name_display',
                                     'data-search-button-id': 'search_employee_btn',
                                     'data-error-target': 'ministry_number_error'})
    )

    class Meta:
        model = Departure
        fields = ['departure_type', 'date', 'time_from', 'time_to', 'reason', 'status']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'time_from': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'time_to': forms.TimeInput(attrs={'type': 'time', 'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
            'departure_type': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        time_from = cleaned_data.get('time_from')
        time_to = cleaned_data.get('time_to')

        if time_from and time_to and time_from >= time_to:
            raise forms.ValidationError('وقت البداية يجب أن يكون قبل وقت النهاية.')

        return cleaned_data
