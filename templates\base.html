{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{% block title %}نظام شؤون الموظفين{% endblock %}</title>
    <!-- Favicon -->
    <link rel="icon" href="{% static 'img/moe_logo.svg' %}" type="image/svg+xml">
    <link rel="shortcut icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <!-- Black and White Theme CSS (Default Theme) -->
    <link rel="stylesheet" href="{% static 'css/black-white-theme.css' %}">
    <!-- Navbar Enhancements CSS -->
    <link rel="stylesheet" href="{% static 'css/navbar-enhancements.css' %}">
    <!-- Navbar Improvements CSS -->
    <link rel="stylesheet" href="{% static 'css/navbar-improvements.css' %}">
    <!-- System Theme CSS - Unified styling for all pages -->
    <link rel="stylesheet" href="{% static 'css/system-theme.css' %}">
    <!-- Table Borders CSS - Adds borders to all tables -->
    <link rel="stylesheet" href="{% static 'css/table-borders.css' %}">

    <!-- Navbar and Footer Fix CSS - Ensures consistent colors -->
    <link rel="stylesheet" href="{% static 'css/navbar-footer-fix.css' %}">
    <!-- Override CSS to fix navbar issues -->
    <style>
        /* Fix navbar color and elements visibility */
        .navbar-search {
            display: block !important;
        }

        /* Force navbar to have exactly the same color as footer */
        .navbar {
            background-color: #222 !important;
        }



        /* White search bar */
        .navbar-search .form-control {
            background-color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: black !important;
        }

        .navbar-search .form-control::placeholder {
            color: rgba(0, 0, 0, 0.5) !important;
        }

        .navbar-search .search-icon {
            color: rgba(0, 0, 0, 0.5) !important;
        }

        @media (max-width: 767.98px) {
            .navbar-search {
                display: none !important;
            }
        }

        /* تنسيق مميز لرابط الصفحة الرئيسية */
        .sidebar-link.home-link {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
            color: white !important;
            font-weight: 700 !important;
            font-size: 1.1rem !important;
            border-radius: 10px !important;
            margin-bottom: 15px !important;
            padding: 15px 20px !important;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
            border: 2px solid rgba(255, 255, 255, 0.1) !important;
            transition: all 0.3s ease !important;
        }

        .sidebar-link.home-link:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4) !important;
            color: white !important;
        }

        .sidebar-link.home-link.active {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4) !important;
        }

        .sidebar-link.home-link i {
            font-size: 1.3rem !important;
            margin-left: 10px !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }

        .sidebar-link.home-link span {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %} d-flex flex-column min-vh-100" style="padding-bottom: 60px;">
    {% if user.is_authenticated and not 'login' in request.path %}
    <!-- Sidebar -->
    <div class="sidebar fade-in" id="sidebarMenu">
        <div class="sidebar-header">
            <a href="{% url 'dashboard' %}" class="sidebar-brand">نظام شؤون الموظفين</a>
        </div>
        <div class="sidebar-menu">
            <!-- الصفحة الرئيسية - مميزة -->
            <div class="sidebar-item">
                <a href="{% url 'home:home' %}" class="sidebar-link home-link {% if request.path == '/' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>الصفحة الرئيسية</span>
                </a>
            </div>

            <div class="sidebar-item">
                <a href="{% url 'home:analytics_dashboard' %}" class="sidebar-link {% if '/analytics-dashboard/' in request.path %}active{% endif %}">
                    <i class="fas fa-chart-line"></i>
                    <span>لوحة تحليلات البيانات</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'dashboard' %}" class="sidebar-link {% if request.path == '/dashboard/' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </div>

            <div class="sidebar-item">
                <a href="#" class="sidebar-link {% if '/employment/' in request.path or '/employees/' in request.path %}active{% endif %}" data-bs-toggle="collapse" data-bs-target="#employmentSubmenu" aria-expanded="false">
                    <i class="fas fa-briefcase"></i>
                    <span>الكادر</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse {% if '/employment/' in request.path or '/employees/' in request.path %}show{% endif %}" id="employmentSubmenu">
                    <div class="sidebar-submenu">
                        {% if request.user.is_superuser or request.user.is_full_admin or 'employees:employee_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employees:employee_list' %}" class="sidebar-link {% if '/employees/' in request.path %}active{% endif %}">
                                <i class="fas fa-users"></i>
                                <span>بيانات الموظفين</span>
                            </a>
                        </div>
                        <div class="sidebar-item">
                            <a href="{% url 'employment:employment_list' %}" class="sidebar-link {% if '/employment/' in request.path and not '/employment/departments/' in request.path and not '/employment/appointment-types/' in request.path and not '/employment/positions/' in request.path and not '/employment/employee-positions/' in request.path and not '/employment/experience-certificates/' in request.path and not '/employment/technical-positions/' in request.path and not '/employment/actual-service/' in request.path and not '/employment/excess-employees/' in request.path and not '/employment/medical-conditions/' in request.path and not '/employment/btec' in request.path and not '/employment/employee-identifications/' in request.path and not '/employment/non-payment/' in request.path %}active{% endif %}">
                                <i class="fas fa-user-tie"></i>
                                <span>إدارة الكادر</span>
                            </a>
                        </div>
                        <div class="sidebar-item">
                            <a href="{% url 'employment:employee_identification_list' %}" class="sidebar-link {% if '/employment/employee-identifications/' in request.path %}active{% endif %}">
                                <i class="fas fa-id-card"></i>
                                <span>البيانات التعريفية</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_full_admin or 'employment:department_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:department_list' %}" class="sidebar-link {% if '/employment/departments/' in request.path %}active{% endif %}">
                                <i class="fas fa-building"></i>
                                <span>الأقسام</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_full_admin or 'employment:appointment_type_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:appointment_type_list' %}" class="sidebar-link {% if '/employment/appointment-types/' in request.path %}active{% endif %}">
                                <i class="fas fa-user-tag"></i>
                                <span>صفة التعيين</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_full_admin or 'employment:position_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:position_list' %}" class="sidebar-link {% if '/employment/positions/' in request.path %}active{% endif %}">
                                <i class="fas fa-id-card"></i>
                                <span>المسميات الوظيفية</span>
                            </a>
                        </div>
                        <div class="sidebar-item">
                            <a href="{% url 'employment:employee_position_list' %}" class="sidebar-link {% if '/employment/employee-positions/' in request.path %}active{% endif %}">
                                <i class="fas fa-exchange-alt"></i>
                                <span>الحراك الوظيفي</span>
                            </a>
                        </div>
                        <div class="sidebar-item">
                            <a href="{% url 'employment:experience_certificate_list' %}" class="sidebar-link {% if '/employment/experience-certificates/' in request.path %}active{% endif %}">
                                <i class="fas fa-certificate"></i>
                                <span>شهادة الخبرة</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_full_admin or 'employment:technical_position_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:technical_position_list' %}" class="sidebar-link {% if '/employment/technical-positions/' in request.path %}active{% endif %}">
                                <i class="fas fa-clipboard-list"></i>
                                <span>الموقف الفني</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'employment:actual_service_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:actual_service_list' %}" class="sidebar-link {% if '/employment/actual-service/' in request.path %}active{% endif %}">
                                <i class="fas fa-clock"></i>
                                <span>الخدمة الفعلية</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'employees:calculate_age' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'employees:calculate_age' %}" class="sidebar-link {% if '/employees/calculate-age/' in request.path %}active{% endif %}">
                                <i class="fas fa-birthday-cake"></i>
                                <span>حساب العمر</span>
                            </a>
                        </div>
                        {% endif %}


                        {% if request.user.is_superuser or request.user.is_admin %}
                        <div class="sidebar-item">
                            <a href="{% url 'home:internal_transfer_list' %}" class="sidebar-link {% if '/internal-transfer-list/' in request.path %}active{% endif %}">
                                <i class="fas fa-exchange-alt"></i>
                                <span>النقل الداخلي</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:excess_employee_list' %}" class="sidebar-link {% if '/employment/excess-employees/' in request.path %}active{% endif %}">
                                <i class="fas fa-user-plus"></i>
                                <span>الموظفين الزوائد</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:medical_condition_list' %}" class="sidebar-link {% if '/employment/medical-conditions/' in request.path %}active{% endif %}">
                                <i class="fas fa-heartbeat"></i>
                                <span>الحالات المرضية</span>
                            </a>
                        </div>
                        <div class="sidebar-item">
                            <a href="{% url 'employment:btec_list' %}" class="sidebar-link {% if '/employment/btec' in request.path %}active{% endif %}">
                                <i class="fas fa-graduation-cap"></i>
                                <span>BTEC</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% if request.user.is_superuser or request.user.is_admin or 'leaves:unpaid_leave_list' in user_visible_pages or 'disciplinary:penalty_list' in user_visible_pages or 'disciplinary:penalty_type_list' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="#" class="sidebar-link {% if '/leaves/unpaid/' in request.path or '/disciplinary/' in request.path %}active{% endif %}" data-bs-toggle="collapse" data-bs-target="#leavesSubmenu" aria-expanded="false">
                    <i class="fas fa-tasks"></i>
                    <span>الإجراءات</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse {% if '/leaves/unpaid/' in request.path or '/disciplinary/' in request.path %}show{% endif %}" id="leavesSubmenu">
                    <div class="sidebar-submenu">
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:unpaid_leave_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:unpaid_leave_list' %}" class="sidebar-link {% if '/leaves/unpaid/' in request.path %}active{% endif %}">
                                <i class="fas fa-calendar-times"></i>
                                <span>إجازات بدون راتب</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'disciplinary:penalty_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'disciplinary:penalty_list' %}" class="sidebar-link {% if '/disciplinary/penalties' in request.path %}active{% endif %}">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>العقوبات</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'disciplinary:penalty_type_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'disciplinary:penalty_type_list' %}" class="sidebar-link {% if '/disciplinary/penalty-types' in request.path %}active{% endif %}">
                                <i class="fas fa-list"></i>
                                <span>أنواع العقوبات</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin %}
                        <div class="sidebar-item">
                            <a href="{% url 'employment:non_payment_list' %}" class="sidebar-link {% if '/employment/non-payment/' in request.path %}active{% endif %}">
                                <i class="fas fa-ban"></i>
                                <span>عدم الصرف</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            {% if request.user.is_superuser or request.user.is_admin or 'leaves:leave_list' in user_visible_pages or 'leaves:leave_create' in user_visible_pages or 'leaves:leave_balance_list' in user_visible_pages or 'leaves:leave_reports' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="#" class="sidebar-link {% if '/leaves/' in request.path and not '/leaves/unpaid/' in request.path %}active{% endif %}" data-bs-toggle="collapse" data-bs-target="#regularLeavesSubmenu" aria-expanded="false">
                    <i class="fas fa-calendar-check"></i>
                    <span>إجازات الموظفين</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse {% if '/leaves/' in request.path and not '/leaves/unpaid/' in request.path %}show{% endif %}" id="regularLeavesSubmenu">
                    <div class="sidebar-submenu">
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:leave_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:leave_list' %}" class="sidebar-link {% if '/leaves/' in request.path and not '/leaves/unpaid/' in request.path and not '/leaves/balance/' in request.path and not '/leaves/reports/' in request.path %}active{% endif %}">
                                <i class="fas fa-list"></i>
                                <span>قائمة الإجازات</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:leave_create' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:leave_create' %}" class="sidebar-link {% if '/leaves/add/' in request.path %}active{% endif %}">
                                <i class="fas fa-plus"></i>
                                <span>إضافة إجازة</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:leave_balance_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:leave_balance_list' %}" class="sidebar-link {% if '/leaves/balance/' in request.path %}active{% endif %}">
                                <i class="fas fa-calculator"></i>
                                <span>رصيد الإجازات</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:departure_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:departure_list' %}" class="sidebar-link {% if '/leaves/departures/' in request.path %}active{% endif %}">
                                <i class="fas fa-plane-departure"></i>
                                <span>المغادرات</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'leaves:leave_reports' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'leaves:leave_reports' %}" class="sidebar-link {% if '/leaves/reports/' in request.path %}active{% endif %}">
                                <i class="fas fa-chart-bar"></i>
                                <span>تقارير الإجازات</span>
                            </a>
                        </div>
                        {% endif %}

                    </div>
                </div>
            </div>
            {% endif %}

            {% if request.user.is_superuser or request.user.is_admin or 'employees:maternity_leaves_list' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="{% url 'employees:maternity_leaves_list' %}" class="sidebar-link {% if '/employees/maternity-leaves/' in request.path %}active{% endif %}">
                    <i class="fas fa-baby"></i>
                    <span>إجازات الأمومة</span>
                </a>
            </div>
            {% endif %}

            {% if request.user.is_superuser or request.user.is_admin or 'file_management:file_movement_list' in user_visible_pages or 'file_management:file_checkout' in user_visible_pages or 'file_management:file_return_list' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="#" class="sidebar-link {% if '/files/' in request.path %}active{% endif %}" data-bs-toggle="collapse" data-bs-target="#filesSubmenu" aria-expanded="false">
                    <i class="fas fa-folder"></i>
                    <span>الملفات</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse {% if '/files/' in request.path %}show{% endif %}" id="filesSubmenu">
                    <div class="sidebar-submenu">
                        {% if request.user.is_superuser or request.user.is_admin or 'file_management:file_movement_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'file_management:file_movement_list' %}" class="sidebar-link {% if '/files/' in request.path and not '/files/checkout/' in request.path and not '/files/completed/' in request.path %}active{% endif %}">
                                <i class="fas fa-exchange-alt"></i>
                                <span>حركة الملف</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'file_management:file_checkout' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'file_management:file_checkout' %}" class="sidebar-link {% if '/files/checkout/' in request.path %}active{% endif %}">
                                <i class="fas fa-file-export"></i>
                                <span>تسجيل خروج ملف</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'file_management:file_return_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'file_management:file_return_list' %}" class="sidebar-link {% if '/files/completed/' in request.path %}active{% endif %}">
                                <i class="fas fa-check-circle"></i>
                                <span>الملفات المنجزة</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if request.user.is_superuser or request.user.is_admin or 'ranks:rank_type_list' in user_visible_pages or 'ranks:employee_rank_create' in user_visible_pages or 'ranks:employee_rank_list' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="#" class="sidebar-link {% if '/ranks/' in request.path %}active{% endif %}" data-bs-toggle="collapse" data-bs-target="#ranksSubmenu" aria-expanded="false">
                    <i class="fas fa-medal"></i>
                    <span>الرتب والعلاوات</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse {% if '/ranks/' in request.path %}show{% endif %}" id="ranksSubmenu">
                    <div class="sidebar-submenu">
                        {% if request.user.is_superuser or request.user.is_admin or 'ranks:rank_type_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'ranks:rank_type_list' %}" class="sidebar-link {% if '/ranks/types/' in request.path %}active{% endif %}">
                                <i class="fas fa-list"></i>
                                <span>أنواع الرتب</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'ranks:employee_rank_create' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'ranks:employee_rank_create' %}" class="sidebar-link {% if '/ranks/add/' in request.path %}active{% endif %}">
                                <i class="fas fa-plus-circle"></i>
                                <span>إضافة رتبة للموظف</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin or 'ranks:employee_rank_list' in user_visible_pages %}
                        <div class="sidebar-item">
                            <a href="{% url 'ranks:employee_rank_list' %}" class="sidebar-link {% if '/ranks/' in request.path and not '/ranks/types/' in request.path and not '/ranks/add/' in request.path and not '/ranks/allowances/' in request.path %}active{% endif %}">
                                <i class="fas fa-list-alt"></i>
                                <span>رتب الموظفين</span>
                            </a>
                        </div>
                        {% endif %}
                        {% if request.user.is_superuser or request.user.is_admin %}
                        <div class="sidebar-item">
                            <a href="{% url 'ranks:employee_allowance_list' %}" class="sidebar-link {% if '/ranks/allowances/' in request.path %}active{% endif %}">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>العلاوات</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            {% if request.user.is_superuser or request.user.is_admin or 'performance:performance_list' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="{% url 'performance:performance_list' %}" class="sidebar-link {% if '/performance/' in request.path %}active{% endif %}">
                    <i class="fas fa-file-alt"></i>
                    <span>التقارير السنوية</span>
                </a>
            </div>
            {% endif %}

            {% if request.user.is_superuser or request.user.is_admin %}
            <div class="sidebar-item">
                <a href="{% url 'home:approved_forms_admin' %}" class="sidebar-link {% if '/approved-forms/admin/' in request.path %}active{% endif %}">
                    <i class="fas fa-file-upload"></i>
                    <span>إدارة النماذج المعتمدة</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'home:important_links_admin' %}" class="sidebar-link {% if '/important-links-admin/' in request.path %}active{% endif %}">
                    <i class="fas fa-link"></i>
                    <span>إدارة الروابط المهمة</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'announcements:announcements_list' %}" class="sidebar-link {% if '/announcements/' in request.path %}active{% endif %}">
                    <i class="fas fa-bullhorn"></i>
                    <span>إدارة الإعلانات</span>
                </a>
            </div>
            {% endif %}
            {% if request.user.is_superuser or request.user.is_admin or 'reports:report_dashboard' in user_visible_pages %}
            <div class="sidebar-item">
                <a href="{% url 'reports:report_dashboard' %}" class="sidebar-link {% if '/reports/' in request.path %}active{% endif %}">
                    <i class="fas fa-chart-pie"></i>
                    <span>تقارير النظام</span>
                </a>
            </div>
            {% endif %}
            {% if user.is_staff or user.is_admin %}
            <div class="sidebar-item">
                <a href="{% url 'accounts:user_list' %}" class="sidebar-link {% if '/accounts/' in request.path and not '/profile/' in request.path and not '/change-password/' in request.path %}active{% endif %}">
                    <i class="fas fa-user-cog"></i>
                    <span>المستخدمين</span>
                </a>
            </div>
            {% endif %}

            {% if user.is_superuser %}
            <div class="sidebar-item">
                <a href="{% url 'backup:backup_list' %}" class="sidebar-link {% if '/backup/' in request.path %}active{% endif %}">
                    <i class="fas fa-database"></i>
                    <span>النسخ الاحتياطية</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'system_logs:system_log_list' %}" class="sidebar-link {% if '/system-logs/' in request.path and not '/errors/' in request.path %}active{% endif %}">
                    <i class="fas fa-history"></i>
                    <span>سجل حركات النظام</span>
                </a>
            </div>
            
            <div class="sidebar-item">
                <a href="{% url 'system_logs:system_error_list' %}" class="sidebar-link {% if '/system-logs/errors/' in request.path %}active{% endif %}">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    <span>سجل أخطاء النظام</span>
                </a>
            </div>
            {% endif %}
            <div class="sidebar-item mt-4">
                <a href="{% url 'accounts:profile' %}" class="sidebar-link {% if '/profile/' in request.path and not '/instructions/' in request.path %}active{% endif %}">
                    <i class="fas fa-user"></i>
                    <span>الملف الشخصي</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'accounts:instructions' %}" class="sidebar-link {% if '/instructions/' in request.path %}active{% endif %}">
                    <i class="fas fa-book"></i>
                    <span>الدليل الإرشادي</span>
                </a>
            </div>
            <div class="sidebar-item">
                <a href="{% url 'accounts:change_password' %}" class="sidebar-link {% if '/change-password/' in request.path %}active{% endif %}">
                    <i class="fas fa-key"></i>
                    <span>تغيير كلمة المرور</span>
                </a>
            </div>

            <div class="sidebar-item">
                <a href="{% url 'home:about' %}" class="sidebar-link {% if '/about/' in request.path %}active{% endif %}">
                    <i class="fas fa-info-circle"></i>
                    <span>حول</span>
                </a>
            </div>

            <div class="sidebar-item">
                <a href="{% url 'accounts:logout' %}" class="sidebar-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top fade-in">
        <div class="container-fluid">
            <button id="sidebarToggleBtn" class="btn btn-link d-lg-none me-2">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand d-lg-none" href="{% url 'dashboard' %}">
                <i class="fas fa-user-tie"></i>
                نظام شؤون الموظفين
            </a>

            <div class="d-flex align-items-center ms-auto">
                <!-- Search Bar -->
                <div class="navbar-search d-none d-md-block">
                    <form action="{% url 'home:search' %}" method="GET">
                        <div class="position-relative">
                            <input type="text" name="q" class="form-control" placeholder="بحث في النظام..." aria-label="بحث">
                            <div class="search-icon">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </form>
                </div>



                <!-- Notifications -->
                <div class="dropdown me-3">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" id="notificationsToggle">
                        <i class="fas fa-bell"></i>
                        {% if unread_notifications_count > 0 %}
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationBadge">
                            {{ unread_notifications_count }}
                        </span>
                        {% endif %}
                    </a>
                    <div class="dropdown-menu dropdown-menu-end" style="width: 350px;" id="notificationsDropdown">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>الإشعارات</span>
                            {% if unread_notifications_count > 0 %}
                            <button class="btn btn-sm btn-outline-primary" id="markAllAsRead">
                                تحديد الكل كمقروء
                            </button>
                            {% endif %}
                        </div>
                        <div class="dropdown-divider"></div>
                        <div id="notificationsList">
                            {% if notifications %}
                                {% for notification in notifications %}
                                <a class="dropdown-item d-flex align-items-center {% if not notification.is_read %}bg-light{% endif %}" href="{% url 'notifications:notification_list' %}" data-notification-id="{{ notification.id }}">
                                    <div class="me-3">
                                        <div class="{{ notification.get_background_color }} rounded-circle p-2 d-flex justify-content-center align-items-center" style="width: 40px; height: 40px;">
                                            <i class="fas {{ notification.icon }} text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="small text-muted">{{ notification.get_time_since_created }}</div>
                                        <div class="fw-bold">{{ notification.title }}</div>
                                        <div class="small">{{ notification.message|truncatechars:50 }}</div>
                                    </div>
                                    {% if not notification.is_read %}
                                    <div class="ms-2">
                                        <span class="badge bg-primary">جديد</span>
                                    </div>
                                    {% endif %}
                                </a>
                                {% if not forloop.last %}<div class="dropdown-divider"></div>{% endif %}
                                {% endfor %}
                            {% else %}
                                <div class="dropdown-item text-center text-muted py-4">
                                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                                    <div>لا توجد إشعارات</div>
                                </div>
                            {% endif %}
                        </div>
                        {% if notifications %}
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-center small text-primary" href="{% url 'notifications:notification_list' %}">
                            عرض جميع الإشعارات
                        </a>
                        {% endif %}
                    </div>
                </div>

                <!-- User Profile -->
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <div class="user-info-container">
                            <span class="username">{{ user.username }}</span>
                            <span class="account-type">
                                {% if user.is_superuser %}
                                    مدير النظام
                                {% elif user.is_staff %}
                                    مشرف
                                {% else %}
                                    مستخدم
                                {% endif %}
                            </span>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:instructions' %}">الدليل الإرشادي</a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:change_password' %}">تغيير كلمة المرور</a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:reload_permissions' %}?next={{ request.path }}">إعادة تحميل الصلاحيات</a></li>
                        <li><a class="dropdown-item" href="{% url 'home:about' %}">حول النظام</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}

    <!-- Main Content -->
    <div class="main-content flex-grow-1">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-permanent alert-dismissible fade show" role="alert">
                <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'info' %}fa-info-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% elif message.tags == 'error' or message.tags == 'danger' %}fa-times-circle{% else %}fa-bell{% endif %}"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="content-wrapper">
            <div class="container-fluid">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    {% else %}
    <!-- Login page doesn't need sidebar or navbar -->
    <div class="login-content">
        {% block content_login %}{% endblock %}
    </div>
    {% endif %}

    <!-- Footer -->
    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <span class="text-muted">جميع الحقوق محفوظة مديرية قصبة المفرق - المبرمج احمد العمري</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>
    <script src="{% static 'js/permissions.js' %}"></script>

    <script>
        // Initialize Select2 for all select elements with the 'select2' class
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                language: 'ar',
                dir: 'rtl',
                width: '100%',
                placeholder: 'اختر...',
                allowClear: true
            });

            // Add event listener to reload permissions link
            document.querySelector('a[href*="reload_permissions"]').addEventListener('click', function(e) {
                e.preventDefault();
                reloadPermissions();
            });

            // Notifications functionality
            initNotifications();
        });

        function initNotifications() {
            // Mark all as read
            $('#markAllAsRead').on('click', function(e) {
                e.preventDefault();
                $.post('{% url "notifications:mark_all_read" %}', {
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                })
                .done(function(response) {
                    if (response.success) {
                        $('#notificationBadge').hide();
                        $('.dropdown-item[data-notification-id]').removeClass('bg-light');
                        $('.badge:contains("جديد")').remove();
                        $('#markAllAsRead').parent().hide();
                        location.reload(); // Reload to update the context
                    }
                })
                .fail(function() {
                    alert('حدث خطأ أثناء تحديث الإشعارات');
                });
            });

            // Bind initial notification events
            bindNotificationEvents();

            // Initial load of notifications
            refreshNotifications();

            // Auto-refresh notifications every 30 seconds
            setInterval(function() {
                refreshNotifications();
            }, 30000);
        }

        function refreshNotifications() {
            $.get('{% url "notifications:get_notifications_ajax" %}')
            .done(function(response) {
                if (response && typeof response.unread_count !== 'undefined') {
                    updateNotificationBadge(response.unread_count);
                    updateNotificationsList(response.notifications || []);
                } else {
                    console.log('Invalid response format');
                }
            })
            .fail(function(xhr, status, error) {
                console.log('Failed to refresh notifications:', error);
                // Show fallback state
                updateNotificationBadge(0);
                updateNotificationsList([]);
            });
        }

        function updateNotificationBadge(count) {
            if (count > 0) {
                $('#notificationBadge').text(count).show();
            } else {
                $('#notificationBadge').hide();
            }
        }

        function updateNotificationsList(notifications) {
            var $notificationsList = $('#notificationsList');
            var $markAllReadBtn = $('#markAllAsRead');

            if (!notifications || notifications.length === 0) {
                $notificationsList.html(`
                    <div class="dropdown-item text-center text-muted py-4">
                        <i class="fas fa-bell-slash fa-2x mb-2"></i>
                        <div>لا توجد إشعارات</div>
                    </div>
                `);
                $markAllReadBtn.hide();
                return;
            }

            var html = '';
            var hasUnread = false;

            notifications.forEach(function(notification, index) {
                var isUnread = !notification.is_read;
                if (isUnread) hasUnread = true;

                html += `
                    <a class="dropdown-item d-flex align-items-center ${isUnread ? 'bg-light' : ''}"
                       href="{% url 'notifications:notification_list' %}" data-notification-id="${notification.id}">
                        <div class="me-3">
                            <div class="${notification.background_color} rounded-circle p-2 d-flex justify-content-center align-items-center"
                                 style="width: 40px; height: 40px;">
                                <i class="fas ${notification.icon} text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-muted">${notification.time_since}</div>
                            <div class="fw-bold">${notification.title}</div>
                            <div class="small">${notification.message.length > 50 ? notification.message.substring(0, 50) + '...' : notification.message}</div>
                        </div>
                        ${isUnread ? '<div class="ms-2"><span class="badge bg-primary">جديد</span></div>' : ''}
                    </a>
                `;

                if (index < notifications.length - 1) {
                    html += '<div class="dropdown-divider"></div>';
                }
            });

            $notificationsList.html(html);

            if (hasUnread) {
                $markAllReadBtn.show();
            } else {
                $markAllReadBtn.hide();
            }

            // Re-bind click events for new notification items
            bindNotificationEvents();
        }

        function bindNotificationEvents() {
            $('.dropdown-item[data-notification-id]').off('click').on('click', function(e) {
                var notificationId = $(this).data('notification-id');
                var $item = $(this);
                var href = $(this).attr('href');

                // If notification is unread, mark it as read first
                if ($item.hasClass('bg-light')) {
                    e.preventDefault(); // Prevent immediate navigation

                    $.post('{% url "notifications:mark_read" notification_id=0 %}'.replace('0', notificationId), {
                        'csrfmiddlewaretoken': '{{ csrf_token }}'
                    })
                    .done(function(response) {
                        if (response.success) {
                            $item.removeClass('bg-light');
                            $item.find('.badge:contains("جديد")').remove();

                            // Update badge count
                            var currentCount = parseInt($('#notificationBadge').text()) || 0;
                            if (currentCount > 1) {
                                $('#notificationBadge').text(currentCount - 1);
                            } else {
                                $('#notificationBadge').hide();
                            }

                            // Hide mark all as read button if no unread notifications
                            if ($('#notificationsList .bg-light').length === 0) {
                                $('#markAllAsRead').hide();
                            }
                        }

                        // Navigate to notifications page after marking as read
                        window.location.href = href;
                    })
                    .fail(function() {
                        console.log('Failed to mark notification as read');
                        // Navigate anyway
                        window.location.href = href;
                    });
                }
                // If already read, allow normal navigation
            });
        }
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
